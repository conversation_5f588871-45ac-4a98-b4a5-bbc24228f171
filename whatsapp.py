#!/usr/bin/env python3
"""
Production WhatsApp Test - Using Exact Working Method
Using the exact _send_message_direct method from your working codebase
"""

import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def send_message_direct():
    """Send message using the exact production method"""
    logger.info("🎯 Using production _send_message_direct method...")
    
    # Your phone number formatted as used in production
    phone = "919652696490"  # With 91 prefix as used in production
    
    # Use the exact Hindi message template from production
    message = "नमस्ते Customer, आपका ऑर्डर OID12345 जिसकी कीमत ₹500 है, 24 घंटों के अंदर डिलीवर कर दिया जाएगा। -Team Rozana"
    
    # Exact parameters from _send_message_direct method
    params = {
        "userid": "2000245696",     # From get_auth_params()
        "password": "*WchbXCp",     # From get_auth_params()
        "method": "SENDMESSAGE",    # Uppercase as in production
        "send_to": phone,
        "msg": message,
        "msg_type": "TEXT",
        "format": "json",
        "v": "1.1"
    }
    
    url = "https://media.smsgupshup.com/GatewayAPI/rest"
    
    try:
        logger.info(f"Sending direct message to {phone}")
        logger.info(f"Parameters: {params}")
        
        response = requests.get(url, params=params, timeout=30)
        
        logger.info(f"Response status: {response.status_code}")
        logger.info(f"Response body: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("response", {}).get("status") == "success":
                logger.info("✅ Direct message sent successfully!")
                print("✅ Message sent using production method!")
                print("📱 Check your WhatsApp for the Hindi order confirmation message")
                return True
            else:
                logger.error(f"API error: {result}")
        else:
            logger.error(f"HTTP error: {response.status_code}")
        
        return False
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return False

def send_english_version():
    """Send English version of the same message"""
    logger.info("🌐 Sending English version...")
    
    phone = "919652696490"
    message = "Hello Customer, your order OID12345 with amount ₹500 will be delivered within 24 hours. -Team Rozana"
    
    params = {
        "userid": "2000245696",
        "password": "*WchbXCp",
        "method": "SENDMESSAGE",
        "send_to": phone,
        "msg": message,
        "msg_type": "TEXT",
        "format": "json",
        "v": "1.1"
    }
    
    url = "https://media.smsgupshup.com/GatewayAPI/rest"
    
    try:
        response = requests.get(url, params=params, timeout=30)
        logger.info(f"English message - Status: {response.status_code}")
        logger.info(f"English message - Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("response", {}).get("status") == "success":
                logger.info("✅ English message sent successfully!")
                return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error sending English message: {str(e)}")
        return False

def main():
    print("🚀 Production WhatsApp Test")
    print("=" * 50)
    
    # Test 1: Hindi message (exact production format)
    for i in range(3):
        success1 = send_message_direct()
    
    print("\n" + "=" * 50)
    
    # Test 2: English version
    success2 = send_english_version()
    
    print("\n" + "=" * 50)
    print("📊 Final Results:")
    
    if success1 or success2:
        print("✅ At least one message was sent successfully!")
        print("📱 Please check your WhatsApp (messages might take 1-2 minutes)")
        print("🔍 If you still don't receive messages, possible causes:")
        print("   • WhatsApp Business API account needs activation")
        print("   • Phone number not properly registered with WhatsApp Business")
        print("   • Gupshup account needs WhatsApp Business verification")
    else:
        print("❌ Both tests failed - check logs for details")

if __name__ == "__main__":
    main()






