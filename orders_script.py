import mysql.connector
import psycopg2
from psycopg2.extras import execute_values
import random
import string

# ✅ MySQL (AWS RDS)
MYSQL_CONFIG = {
    "host": "read-replica-newdb.cwq2gkdouo5a.ap-south-1.rds.amazonaws.com",
    "user": "nithin",
    "password": "Nithin@#2025",
    "database": "newrozana_dbapi"
}

# ✅ Postgres (Docker container oms_db)
POSTGRES_CONFIG = {
    "host": "localhost",
    "user": "user",
    "password": "password",
    "dbname": "oms_db",
    "port": 5432
}

# Status mapping MySQL → Postgres
STATUS_MAP = {
    "awaiting": 0, #32
    "pending": 10, #6000+
    "open": 23,
    "in_progress": 24,
    "picked": 25,
    "fulfilled": 26,
    "invoiced": 27,
    "cancel": 14,
    "cancelled": 14, 
    "on_review": 13, #1 record
    "in_transit": 32 #1 record
}

# Platform mapping MySQL → Postgres
PLATFORM_MAP = {
    "android": "app",
    "ios": "app",
    "pos": "pos",
    "web": "web"
}

# --- DB Connections ---
mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
mysql_cur = mysql_conn.cursor(dictionary=True)

pg_conn = psycopg2.connect(**POSTGRES_CONFIG)
pg_cur = pg_conn.cursor()

# --- Fetch from MySQL ---
mysql_cur.execute("""
    SELECT 
        o.user_id AS customer_id,
        u.name AS customer_name,
        o.sorting_hub_id,
        sh.stockone_code AS facility_name,
        o.order_status AS status,
        o.grand_total AS total_amount,
        o.updated_at AS eta,
        o.platform AS order_mode,
        o.approved AS is_approved,
        o.created_at AS created_at,
        o.updated_at AS updated_at
    FROM orders o
    LEFT JOIN shorting_hubs sh 
        ON o.sorting_hub_id = sh.user_id
    LEFT JOIN users u
        ON o.user_id = u.id
""")

rows = mysql_cur.fetchall()
print(f'found {len(rows)} records')
exit(1)
pg_data = []

# Function to generate random 4-char string
def generate_prefix():
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))

for r in rows:
    # Set customer_name to 'Unknown' if missing or None, and truncate to 100 chars
    customer_name = (r.get("customer_name") or "Unknown")[:100]
    # Truncate facility_name to 100 chars if present
    facility_name = (r.get("facility_name") or "")[:100]

    raw_platform = (r.get("order_mode") or "").lower()
    platform = PLATFORM_MAP.get(raw_platform, "web")

    random_prefix = generate_prefix()

    pg_data.append((
        random_prefix,                                            # random_prefix
        str(r["customer_id"]),                                    # customer_id
        customer_name,                                            # customer_name
        str(r["sorting_hub_id"]) if r.get("sorting_hub_id") else "",
        facility_name,                                            # facility_name
        STATUS_MAP.get((r.get("status") or "").lower(), 0),       # status
        float(r["total_amount"]),                                 # total_amount
        r["eta"],                                                 # eta
        platform,                                                 # order_mode
        True if r["is_approved"] == 1 else False,                 # is_approved
        r["created_at"],                                          # created_at
        r["updated_at"],                                          # updated_at
    ))

# --- Step 1: Insert into Postgres (without order_id) ---
insert_query = """
    INSERT INTO orders (
        random_prefix, customer_id, customer_name, 
        facility_id, facility_name, status, total_amount, eta, 
        order_mode, is_approved, created_at, updated_at
    )
    VALUES %s
    RETURNING id, random_prefix
"""
execute_values(pg_cur, insert_query, pg_data, page_size=5000)

# No need to update order_id, it is a generated column

pg_conn.commit()

# --- Cleanup ---
mysql_cur.close()
mysql_conn.close()
pg_cur.close()
pg_conn.close()

print(f"✅ Migrated {len(pg_data)} records into Postgres orders table with order_id = random_prefix + Postgres id")
