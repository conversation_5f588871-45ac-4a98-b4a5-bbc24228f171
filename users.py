#!/usr/bin/env python3
import mysql.connector
import pandas as pd

MYSQL_CONFIG = {
    "host": "read-replica-newdb.cwq2gkdouo5a.ap-south-1.rds.amazonaws.com",
    "user": "admin",
    "password": "rozana23456",
    "database": "newrozana_dbapi",
}

def export_customers_with_pandas():
    try:
        print(" Connecting to MySQL...")
        conn = mysql.connector.connect(**MYSQL_CONFIG)

        # Query for required user types
        query = """
            SELECT * 
            FROM users
            WHERE user_type IN ('customer', 'customer_new', 'pos')
        """

        print("Fetching records into pandas DataFrame...")
        df = pd.read_sql(query, conn)   # directly fetches into DataFrame

        if df.empty:
            print("⚠ No matching records found.")
            return

        # Show first 20 rows in console
        print("\nPreview of data:")
        print(df.head(20).to_string(index=False))

        # Export to CSV (fast)
        output_file = "filtered_users.csv"
        df.to_csv(output_file, index=False, encoding="utf-8")

        print(f"\nExported {len(df)} records to {output_file}")

    except mysql.connector.Error as err:
        print(f" Database error: {err}")
    except Exception as e:
        print(f" Unexpected error: {e}")
    finally:
        if 'conn' in locals() and conn.is_connected():
            conn.close()
            print(" Connection closed.")

if __name__ == "__main__":
    export_customers_with_pandas()
