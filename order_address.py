#!/usr/bin/env python3
import json
import mysql.connector
import psycopg2
from psycopg2.extras import execute_values

# ------------------ Database Configs ------------------
MYSQL_CONFIG = {
    "host": "read-replica-newdb.cwq2gkdouo5a.ap-south-1.rds.amazonaws.com",
    "user": "nithin",
    "password": "Nithin@#2025",
    "database": "newrozana_dbapi",
}

POSTGRES_CONFIG = {
    "host": "localhost",   # db1 mapped to 5432 on your machine
    "port": 5432,
    "user": "user",
    "password": "password",
    "dbname": "oms_db",
}


# ------------------ Helper: Extract shipping_address JSON ------------------
def extract_shipping_address(shipping_json: str) -> dict:
    """Extract fields from shipping_address JSON safely."""
    if not shipping_json:
        return {}

    try:
        data = json.loads(shipping_json)
    except Exception:
        return {}

    return {
        "full_name": data.get("name"),
        "phone_number": data.get("phone"),
        "address_line1": data.get("address"),
        "address_line2": None,   # always null
        "city": data.get("city"),
        "state": data.get("state"),
        "postal_code": data.get("postal_code"),
        "country": data.get("country"),
    }


# ------------------ Migration ------------------
def migrate():
    # Connect MySQL
    mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
    mysql_cursor = mysql_conn.cursor(dictionary=True)

    # Connect Postgres
    pg_conn = psycopg2.connect(**POSTGRES_CONFIG)
    pg_cursor = pg_conn.cursor()

    # Disable foreign key checks temporarily
    pg_cursor.execute("SET session_replication_role = replica;")

    # Fetch MySQL rows (only orders, ignore additional_addresses type_of_address)
    mysql_query = """
        SELECT
            o.id AS order_id,
            o.shipping_address,
            o.created_at,
            o.updated_at
        FROM orders o
        WHERE o.shipping_address IS NOT NULL
    """
    print("📥 Executing MySQL query...")
    mysql_cursor.execute(mysql_query)
    rows = mysql_cursor.fetchall()
    print(f"📦 Found {len(rows)} rows in MySQL")

    pg_rows = []
    for row in rows:
        shipping = extract_shipping_address(row["shipping_address"])

        # Apply defaults for NOT NULL columns
        full_name = shipping.get("full_name") or "Unknown"
        phone_number = shipping.get("phone_number") or "0000000000"
        address_line1 = shipping.get("address_line1") or "UNKNOWN_ADDRESS"
        city = shipping.get("city") or "Unknown"
        state = shipping.get("state") or "Unknown"
        postal_code = shipping.get("postal_code") or "000000"
        country = shipping.get("country") or "India"

        # Always default type_of_address = "Home"
        type_of_address = "Home"

        # Build Postgres row (id auto PK → not included)
        pg_row = (
            row["order_id"],    # order_id (FK to orders table)
            full_name,
            phone_number,
            address_line1,
            None,               # address_line2 → always NULL
            city,
            state,
            postal_code,
            country,
            type_of_address,
            row["created_at"],
            row["updated_at"],
            None,               # longitude → NULL
            None                # latitude → NULL
        )
        pg_rows.append(pg_row)

    # Insert into Postgres
    pg_insert_query = """
        INSERT INTO order_addresses (
            order_id,
            full_name,
            phone_number,
            address_line1,
            address_line2,
            city,
            state,
            postal_code,
            country,
            type_of_address,
            created_at,
            updated_at,
            longitude,
            latitude
        ) VALUES %s
        ON CONFLICT DO NOTHING;
    """

    if pg_rows:
        execute_values(pg_cursor, pg_insert_query, pg_rows, page_size=500)
        pg_conn.commit()
        print(f"✅ Inserted {len(pg_rows)} order addresses into PostgreSQL")
    else:
        print("⚠️ No valid rows to insert")

    # Re-enable foreign key checks
    pg_cursor.execute("SET session_replication_role = DEFAULT;")

    # Close connections
    mysql_cursor.close()
    mysql_conn.close()
    pg_cursor.close()
    pg_conn.close()
    print("🔒 Database connections closed.")


if __name__ == "__main__":
    migrate()
