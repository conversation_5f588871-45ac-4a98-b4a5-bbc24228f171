#!/usr/bin/env python3
import mysql.connector
import psycopg2
from psycopg2.extras import execute_values
import json
from decimal import Decimal

# ------------------ Database Configs ------------------
MYSQL_CONFIG = {
    "host": "read-replica-newdb.cwq2gkdouo5a.ap-south-1.rds.amazonaws.com",
    "user": "nithin",
    "password": "Nithin@#2025",
    "database": "newrozana_dbapi",
    "buffered": True
}

POSTGRES_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "user": "user",
    "password": "password",
    "dbname": "oms_db",
}

PAYMENT_STATUS_MAP = {
    "paid": 51,       # COMPLETED
    "refund": 52,     # FAILED
    "refunded": 53,   # REFUNDED
    "unpaid": 50      # PENDING
}

MYSQL_BATCH_SIZE = 10000


# ------------------ Migration Script ------------------
def migrate_payment_details():
    with mysql.connector.connect(**MYSQL_CONFIG) as mysql_conn, mysql_conn.cursor(dictionary=True) as mysql_cur, \
         psycopg2.connect(**POSTGRES_CONFIG) as pg_conn, pg_conn.cursor() as pg_cur:

        # ✅ Load all order IDs from Postgres into memory
        pg_cur.execute("SELECT id FROM orders")
        valid_order_ids = {row[0] for row in pg_cur.fetchall()}
        print(f"📦 Loaded {len(valid_order_ids)} valid order_ids from Postgres")

        total_read, total_inserted = 0, 0

        # ✅ Stream MySQL in chunks
        mysql_cur.execute("""
            SELECT order_id, payment_details, order_create,
                   payment_type, payment_status, created_at, updated_at
            FROM order_payments
        """)

        while True:
            rows = mysql_cur.fetchmany(MYSQL_BATCH_SIZE)
            if not rows:
                break
            total_read += len(rows)

            pg_data = []
            for r in rows:
                try:
                    if r["order_id"] not in valid_order_ids:
                        continue  # 🚨 skip orphan payments

                    payment_details = json.loads(r.get("payment_details") or "{}")
                    order_create = json.loads(r.get("order_create") or "{}")
                    raw_status = (r.get("payment_status") or "").lower()
                    mapped_status = PAYMENT_STATUS_MAP.get(raw_status, 50)

                    payment_id = payment_details.get("id") or order_create.get("id")
                    if not payment_id:
                        payment_id = f"unknown-{r['order_id']}"

                    pg_row = (
                        r["order_id"],
                        payment_id,
                        Decimal(order_create.get("amount", 0)) / 100,
                        r.get("created_at"),
                        r.get("payment_type"),
                        order_create.get("id"),
                        Decimal(order_create.get("amount", 0)) / 100,
                        mapped_status,
                        r.get("created_at"),
                        r.get("updated_at"),
                    )
                    pg_data.append(pg_row)
                except Exception as e:
                    print(f"❌ Skipping record due to error: {e}")

            inserted_count = insert_batch(pg_cur, pg_data)
            pg_conn.commit()
            total_inserted += inserted_count

            print(f"✅ Inserted {inserted_count} rows (Total inserted: {total_inserted}, Read: {total_read})")

        print(f"🎯 Migration completed.")
        print(f"📊 Total records read from MySQL: {total_read}")
        print(f"📊 Total records inserted into Postgres: {total_inserted}")


def insert_batch(pg_cur, rows):
    insert_query = """
        INSERT INTO payment_details (
            order_id, payment_id, payment_amount, payment_date,
            payment_mode, payment_order_id, total_amount,
            payment_status, created_at, updated_at
        )
        VALUES %s
        ON CONFLICT DO NOTHING
    """
    if not rows:
        return 0
    execute_values(pg_cur, insert_query, rows, page_size=1000)
    return len(rows)


if __name__ == "__main__":
    migrate_payment_details()
