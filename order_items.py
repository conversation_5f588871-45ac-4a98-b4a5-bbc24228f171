#!/usr/bin/env python3
import mysql.connector
import psycopg2
from psycopg2.extras import execute_values
from decimal import Decimal

# ------------------ Database Configs ------------------
MYSQL_CONFIG = {
    "host": "read-replica-newdb.cwq2gkdouo5a.ap-south-1.rds.amazonaws.com",
    "user": "nithin",
    "password": "Nithin@#2025",
    "database": "newrozana_dbapi"
}

POSTGRES_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "user": "user",
    "password": "password",
    "dbname": "oms_db"
}

ITEM_STATUS_MAP = {
    "cancel": 14,
    "cancelled": 14,
    "delivered": 11,
    "fulfilled": 11,
    "in_progress": 24,
    "in_transit": 24,
    "on_review": 13,
    "open": 23,
    "out_for_delivery": 23,
    "partial_fulfilled": 12,
    "pending": 10,
    "picked": 25,
    "shipment_created": 23,
    "unfulfilled": 13
}

# Batch size for fetch + insert
BATCH_SIZE = 50000   # tune this (20k–100k works well)


def migrate_order_items():
    # Connect to MySQL (server-side cursor → avoids loading all into RAM)
    mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
    mysql_cur = mysql_conn.cursor(dictionary=True, buffered=False)

    # Connect to Postgres
    pg_conn = psycopg2.connect(**POSTGRES_CONFIG)
    pg_cur = pg_conn.cursor()

    # Disable autocommit for performance
    pg_conn.autocommit = False

    # ✅ Load all valid order IDs from Postgres
    pg_cur.execute("SELECT id FROM orders")
    valid_order_ids = {row[0] for row in pg_cur.fetchall()}
    print(f"📦 Loaded {len(valid_order_ids)} valid order_ids from Postgres")

    mysql_query = """
        SELECT
            od.order_id AS order_id,
            p.sku AS sku,
            p.name AS name,
            od.quantity AS quantity,
            0 AS fulfilled_quantity,
            0 AS delivered_quantity,
            fp.mrp AS unit_price,
            fp.ssp AS sale_price,
            od.item_status AS status,
            od.created_at AS created_at,
            od.updated_at AS updated_at,
            pvm.combo_quantity AS pack_uom_quantity,
            cp.sku_code AS wh_sku,
            pvm.cgst AS cgst,
            pvm.sgst AS sgst,
            pvm.igst AS igst,
            pvm.cess AS cess,
            FALSE AS is_returnable,
            '00' AS return_type,
            0 AS return_window,
            fp.ssp AS selling_price_net
        FROM order_details od
        LEFT JOIN products p ON p.id = od.product_id
        LEFT JOIN product_variant_mappings pvm ON od.variant_mapping_id = pvm.id
        LEFT JOIN final_products fp ON fp.id = pvm.id
        LEFT JOIN combo_products cp ON cp.id = pvm.combo_product_id
    """

    mysql_cur.execute(mysql_query)

    pg_insert_query = """
        INSERT INTO order_items (
            order_id, sku, name, quantity, fulfilled_quantity, delivered_quantity,
            unit_price, sale_price, status, pack_uom_quantity, wh_sku,
            cgst, sgst, igst, cess, is_returnable, return_type, return_window,
            selling_price_net, created_at, updated_at
        ) VALUES %s
        ON CONFLICT DO NOTHING;
    """

    total_inserted, total_read = 0, 0

    while True:
        rows = mysql_cur.fetchmany(BATCH_SIZE)
        if not rows:
            break

        total_read += len(rows)
        pg_data = []

        for r in rows:
            try:
                # 🚨 Skip orphan order_items (not in Postgres orders)
                if r["order_id"] not in valid_order_ids:
                    continue

                status_code = ITEM_STATUS_MAP.get((r.get("status") or "").lower(), 10)

                pg_row = (
                    r["order_id"],   # keep as integer to match orders.id
                    (r["sku"] or "")[:100],
                    (r.get("name") or None)[:200] if r.get("name") else None,
                    Decimal(r["quantity"] or 0),
                    Decimal(r["fulfilled_quantity"] or 0),
                    Decimal(r["delivered_quantity"] or 0),
                    Decimal(r["unit_price"] or 0),
                    Decimal(r["sale_price"] or 0),
                    status_code,
                    int(r["pack_uom_quantity"] or 1),
                    (r.get("wh_sku") or None)[:255] if r.get("wh_sku") else None,
                    Decimal(r["cgst"] or 0),
                    Decimal(r["sgst"] or 0),
                    Decimal(r["igst"] or 0),
                    Decimal(r["cess"] or 0),
                    bool(r["is_returnable"]),
                    r["return_type"],
                    int(r["return_window"] or 0),
                    Decimal(r["selling_price_net"] or 0),
                    r["created_at"],
                    r["updated_at"]
                )
                pg_data.append(pg_row)
            except Exception as e:
                print(f"❌ Skipping record due to error: {e}")

        if pg_data:
            execute_values(pg_cur, pg_insert_query, pg_data, page_size=10000)
            pg_conn.commit()
            total_inserted += len(pg_data)

        print(f"✅ Inserted {total_inserted} records so far (Read {total_read})...")

    # Close connections
    mysql_cur.close()
    mysql_conn.close()
    pg_cur.close()
    pg_conn.close()
    print(f"🎯 Migration completed. Total read: {total_read}, Total inserted: {total_inserted}")


if __name__ == "__main__":
    migrate_order_items()
